Metadata-Version: 2.1
Name: pytest-flask
Version: 1.3.0
Summary: A set of py.test fixtures to test Flask applications.
Home-page: https://github.com/pytest-dev/pytest-flask
Author: <PERSON><PERSON>
Author-email: vital.kud<PERSON><EMAIL>
License: MIT
Project-URL: Source, https://github.com/pytest-dev/pytest-flask
Project-URL: Issue tracker, https://github.com/pytest-dev/pytest-flask/issues
Keywords: pytest,flask,testing
Classifier: Framework :: Pytest
Classifier: Environment :: Plugins
Classifier: Programming Language :: Python
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: License :: OSI Approved :: MIT License
Classifier: Topic :: Software Development :: Testing
Classifier: Development Status :: 5 - Production/Stable
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.7
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: pytest >=5.2
Requires-Dist: Flask
Requires-Dist: Werkzeug
Provides-Extra: docs
Requires-Dist: Sphinx ; extra == 'docs'
Requires-Dist: sphinx-rtd-theme ; extra == 'docs'
Provides-Extra: tests

pytest-flask
============

.. image:: https://img.shields.io/pypi/v/pytest-flask.svg
    :target: https://pypi.python.org/pypi/pytest-flask
    :alt: PyPi version

.. image:: https://img.shields.io/conda/vn/conda-forge/pytest-flask.svg
    :target: https://anaconda.org/conda-forge/pytest-flask
    :alt: conda-forge version

.. image:: https://github.com/pytest-dev/pytest-flask/workflows/build/badge.svg
    :target: https://github.com/pytest-dev/pytest-flask/actions
    :alt: CI status

.. image:: https://img.shields.io/pypi/pyversions/pytest-flask.svg
    :target: https://pypi.org/project/pytest-flask
    :alt: PyPi downloads

.. image:: https://readthedocs.org/projects/pytest-flask/badge/?version=latest
   :target: https://pytest-flask.readthedocs.org/en/latest/
   :alt: Documentation status

.. image:: https://img.shields.io/maintenance/yes/2022?color=blue
    :target: https://github.com/pytest-dev/pytest-flask
    :alt: Maintenance

.. image:: https://img.shields.io/github/last-commit/pytest-dev/pytest-flask?color=blue
    :target: https://github.com/pytest-dev/pytest-flask/commits/master
    :alt: GitHub last commit

.. image:: https://img.shields.io/github/issues-pr-closed-raw/pytest-dev/pytest-flask?color=blue
    :target: https://github.com/pytest-dev/pytest-flask/pulls?q=is%3Apr+is%3Aclosed
    :alt: GitHub closed pull requests

.. image:: https://img.shields.io/github/issues-closed/pytest-dev/pytest-flask?color=blue
    :target: https://github.com/pytest-dev/pytest-flask/issues?q=is%3Aissue+is%3Aclosed
    :alt: GitHub closed issues

.. image:: https://img.shields.io/pypi/dm/pytest-flask?color=blue
    :target: https://pypi.org/project/pytest-flask/
    :alt: PyPI - Downloads

.. image:: https://img.shields.io/github/languages/code-size/pytest-dev/pytest-flask?color=blue
    :target: https://github.com/pytest-dev/pytest-flask
    :alt: Code size

.. image:: https://img.shields.io/badge/license-MIT-blue.svg?color=blue
   :target: https://github.com/pytest-dev/pytest-flask/blob/master/LICENSE
   :alt: License

.. image:: https://img.shields.io/github/issues-raw/pytest-dev/pytest-flask.svg?color=blue
   :target: https://github.com/pytest-dev/pytest-flask/issues
   :alt: Issues

.. image:: https://img.shields.io/badge/code%20style-black-000000.svg
   :target: https://github.com/ambv/black
   :alt: style

An extension of `pytest`_ test runner which
provides a set of useful tools to simplify testing and development
of the Flask extensions and applications.

To view a more detailed list of extension features and examples go to
the `PyPI`_ overview page or
`package documentation`_.

How to start?
-------------

Considering the minimal flask `application factory`_ below in ``myapp.py`` as an example:

.. code-block:: python

   from flask import Flask

   def create_app():
      # create a minimal app
      app = Flask(__name__)

      # simple hello world view
      @app.route('/hello')
      def hello():
         return 'Hello, World!'

      return app

You first need to define your application fixture in ``conftest.py``:

.. code-block:: python

    from myapp import create_app

    @pytest.fixture
    def app():
        app = create_app()
        return app

Finally, install the extension with dependencies and run your test suite::

    $ pip install pytest-flask
    $ pytest

Contributing
------------

Don’t hesitate to create a `GitHub issue`_ for any bug or
suggestion. For more information check our contribution `guidelines`_.

.. _pytest: https://docs.pytest.org/en/stable/
.. _PyPI: https://pypi.python.org/pypi/pytest-flask
.. _Github issue: https://github.com/vitalk/pytest-flask/issues
.. _package documentation: http://pytest-flask.readthedocs.org/en/latest/
.. _guidelines: https://github.com/pytest-dev/pytest-flask/blob/master/CONTRIBUTING.rst
.. _application factory: https://flask.palletsprojects.com/patterns/appfactories/

.. _changelog:

Changelog
=========

1.3.0 (2023-10-23)
------------------

- Fix compatibility with ``Flask 3.0`` -- the consequence is that the deprecated and incompatible ``request_ctx`` has been removed.

1.2.1
------------------
- Fix bug in ``:meth:pytest_flask.fixtures.live_server``
  where ``SESSION_COOKIE_DOMAIN`` was set to false due to
  ``original_server_name`` defaulting to "localhost".
  The new default is "localhost.localdomain".
- Drop support for python 3.6 and 3.5

1.2.0 (2021-02-26)
------------------

- Remove deprecated ``:meth:live_server.url``
- fixture ``request_ctx is now deprecated``
  and will be removed in the future
- ``JSONReponse.json`` removed in favour of
  ``Werkzeug.wrappers.Response.json``

1.1.0 (2020-11-08)
------------------

- Speedup live server start time. Use `socket` instead of server
  pulling (`#58`_) to check server availability and add new
  ``--live-server-wait`` option to set the live server wait timeout.
  Thanks to `@jadkik`_.


1.0.0 (2020-03-03)
------------------

**Important**

- ``live_server`` is now ``session``-scoped by default. This can be changed by using the ``live-server_scope`` option in your ``pytest.ini`` (`#113`_). Thanks `@havok2063`_ for the initial patch and `@TWood67`_ for finishing it up.

- pytest 5.2 or later is now required.

- Python 2.7 and 3.4 are no longer supported.

.. _@havok2063: https://github.com/havok2063
.. _@TWood67: https://github.com/TWood67
.. _#113: https://github.com/pytest-dev/pytest-flask/pull/113

0.15.1 (2020-02-03)
-------------------

- Fix ``ImportError`` with ``Werkzeug 1.0.0rc1`` (`#105`_).

.. _#105: https://github.com/pytest-dev/pytest-flask/pull/105

0.15.0 (2019-05-13)
-------------------

- Properly register the ``options`` marker (`#97`_).

.. _#97: https://github.com/pytest-dev/pytest-flask/pull/97

0.14.0 (2018-10-15)
-------------------

- New ``--live-server-host`` command-line option to set the host name used by
  the ``live_server`` fixture.

  Thanks `@o1da`_ for the PR (`#90`_).

.. _@o1da: https://github.com/o1da
.. _#90: https://github.com/pytest-dev/pytest-flask/pull/90

0.13.0 (2018-09-29)
-------------------

- ``JSONReponse`` now supports comparison directly with status codes:

  .. code-block:: python

      assert client.get('invalid-route', headers=[('Accept', 'application/json')]) == 404

  Thanks `@dusktreader`_ for the PR (`#86`_).

.. _@dusktreader: https://github.com/dusktreader
.. _#86: https://github.com/pytest-dev/pytest-flask/pull/86

0.12.0 (2018-09-06)
-------------------

- ``pytest-flask`` now requires ``pytest>=3.6`` (`#84`_).

- Add new ``--live-server-port`` option to select the port the live server will use (`#82`_).
  Thanks `@RazerM`_ for the PR.

- Now ``live_server`` will try to stop the server cleanly by emitting a ``SIGINT`` signal and
  waiting 5 seconds for the server to shutdown. If the server is still running after 5 seconds,
  it will be forcefully terminated. This behavior can be changed by passing
  ``--no-live-server-clean-stop`` in the command-line (`#49`_).
  Thanks `@jadkik`_ for the PR.

- Internal fixes silence pytest warnings, more visible now with ``pytest-3.8.0`` (`#84`_).

.. _@jadkik: https://github.com/jadkik
.. _@RazerM: https://github.com/RazerM
.. _#49: https://github.com/pytest-dev/pytest-flask/issues/49
.. _#82: https://github.com/pytest-dev/pytest-flask/pull/82
.. _#84: https://github.com/pytest-dev/pytest-flask/pull/84


0.11.0 (compared to 0.10.0)
---------------------------

- Implement deployment using Travis, following in line with many other pytest plugins.

- Allow live server to handle concurrent requests (`#56`_), thanks to
  `@mattwbarry`_ for the PR.

- Fix broken link to pytest documentation (`#50`_), thanks to
  `@jineshpaloor`_ for the PR.

- Tox support (`#48`_), thanks to `@steenzout`_ for the PR.

- Add ``LICENSE`` into distribution (`#43`_), thanks to `@danstender`_.

- Minor typography improvements in documentation.

- Add changelog to documentation.


.. _#43: https://github.com/vitalk/pytest-flask/issues/43
.. _#48: https://github.com/pytest-dev/pytest-flask/pull/48
.. _#50: https://github.com/pytest-dev/pytest-flask/pull/50
.. _#56: https://github.com/pytest-dev/pytest-flask/pull/56
.. _#58: steenzouthttps://github.com/pytest-dev/pytest-flask/pull/58
.. _@danstender: https://github.com/danstender
.. _@jadkik: https://github.com/jadkik
.. _@jineshpaloor: https://github.com/jineshpaloor
.. _@mattwbarry: https://github.com/mattwbarry
.. _@steenzout: https://github.com/steenzout


0.10.0 (compared to 0.9.0)
--------------------------

- Add ``--start-live-server``/``--no-start-live-server`` options to prevent
  live server from starting automatically (`#36`_), thanks to `@EliRibble`_.

- Fix title formatting in documentation.


.. _#36: https://github.com/vitalk/pytest-flask/issues/36
.. _@EliRibble: https://github.com/EliRibble


0.9.0 (compared to 0.8.1)
-------------------------

- Rename marker used to pass options to application, e.g. ``pytest.mark.app``
  is now ``pytest.mark.options`` (`#35`_).

- Documentation badge points to the package documentation.

- Add Travis CI configuration to ensure the tests are passed in supported
  environments (`#32`_).


.. _#32: https://github.com/vitalk/pytest-flask/issues/32
.. _#35: https://github.com/vitalk/pytest-flask/issues/35

0.8.1
-----

- Minor changes in documentation.

0.8.0
-----

- New ``request_ctx`` fixture which contains all request relevant
  information (`#29`_).

.. _#29: https://github.com/vitalk/pytest-flask/issues/29

0.7.5
-----

- Use pytest ``monkeypath`` fixture to teardown application config (`#27`_).

.. _#27: https://github.com/vitalk/pytest-flask/issues/27

0.7.4
-----

- Better test coverage, e.g. tests for available fixtures and markers.

0.7.3
-----

- Use retina-ready badges in documentation (`#21`_).

.. _#21: https://github.com/vitalk/pytest-flask/issues/21

0.7.2
-----

- Use pytest ``monkeypatch`` fixture to rewrite live server name.

0.7.1
-----

- Single-sourcing package version (`#24`_), as per `"Python Packaging User Guide"
  <https://packaging.python.org/en/latest/single_source_version.html#single-sourcing-the-version>`_.

.. _#24: https://github.com/vitalk/pytest-flask/issues/24

0.7.0
-----

- Add package documentation (`#20`_).

.. _#20: https://github.com/vitalk/pytest-flask/issues/20

0.6.3
-----

- Better documentation in README with reST formatting (`#18`_), thanks
  to `@greedo`_.


.. _#18: https://github.com/vitalk/pytest-flask/issues/18
.. _@greedo: https://github.com/greedo

0.6.2
-----

- Release the random port before starting the application live server (`#17`_),
  thanks to `@davehunt`_.


.. _#17: https://github.com/vitalk/pytest-flask/issues/17
.. _@davehunt: https://github.com/davehunt

0.6.1
-----

- Bind live server to a random port instead of 5000 or whatever is passed on
  the command line, so it’s possible to execute tests in parallel via
  pytest-dev/pytest-xdist (`#15`_). Thanks to `@davehunt`_.

- Remove ``--liveserver-port`` option.


.. _#15: https://github.com/vitalk/pytest-flask/issues/15
.. _@davehunt: https://github.com/davehunt

0.6.0
-----

- Fix typo in option help for ``--liveserver-port``, thanks to `@svenstaro`_.

.. _@svenstaro: https://github.com/svenstaro

0.5.0
-----

- Add ``live_server`` fixture uses to run application in the background (`#11`_),
  thanks to `@svenstaro`_.


.. _#11: https://github.com/vitalk/pytest-flask/issues/11
.. _@svenstaro: https://github.com/svenstaro

0.4.0
-----

- Add ``client_class`` fixture for class-based tests.

0.3.4
-----

- Include package requirements into distribution (`#8`_).

.. _#8: https://github.com/vitalk/pytest-flask/issues/8

0.3.3
-----

- Explicitly pin package dependencies and their versions.

0.3.2
-----

- Use ``codecs`` module to open files to prevent possible errors on open
  files which contains non-ascii characters.

0.3.1
-----

First release on PyPI.

The MIT License (MIT)

Copyright © 2014–2016 Vital Kudzelka and contributors.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the “Software”), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
